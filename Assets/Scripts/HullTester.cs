
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

[ExecuteInEditMode]
public class HullTester:MonoBehaviour
{
    public List<Transform> Transforms;

    [Header("可视化选项")] 
    public bool DrawIsCollided; // 绘制碰撞状态
    public bool DrawIntersection; // 绘制相交区域

    private Dictionary<int, TestShape> Hulls;

    private void Update()
    {
        HandleTransformChanged();
        HandleHullCollisions();
    }

    /// <summary>
    /// 处理transform变化
    /// </summary>
    public void HandleTransformChanged()
    {
        List<Transform> transforms = Transforms.ToList().Distinct().Where(t => t.gameObject.activeSelf).ToList();
        bool newTransformFound = false;
        int transformCount = 0;

        for (int i = 0; i < transforms.Count; ++i)
        {
            Transform t = transforms[i];
            
            if (t == null)
                continue;

            transformCount++;

            bool foundNewHull = !Hulls.ContainsKey(t.GetInstanceID());

            if (foundNewHull)
            {
                newTransformFound = true;
                break;
            }
        }

        if (!newTransformFound && transformCount == Hulls.Count)
            return;
        
        //重建
        Debug.Log("重建对象");
        
        //释放资源
        EnsureDestroyed();
        
        //收集新的几何体，GetInstanceID对于每个tranform都是唯一不变的，所以可以作为key
        Hulls = transforms.Where(t => t != null).ToDictionary(k => k.GetInstanceID(), CreateShape);
        
        //重绘场景视图
        SceneView.RepaintAll();
    }
    
    /// <summary>
    /// 处理碰撞
    /// </summary>
    public void HandleHullCollisions()
    {
    
    }
    
    //安全释放
    private void EnsureDestroyed()
    {
        if (Hulls == null)
            return;

        foreach (var kvp in Hulls)
        {
            if (kvp.Value.Hull.IsValid)
            {
                kvp.Value.Hull.Dispose();                
            }
        }
        Hulls.Clear();
    }

    private void OnDisable() => EnsureDestroyed();

    private void OnDestroy() => EnsureDestroyed();



    //创建几何体
    public TestShape CreateShape(Transform v)
    {
        Collider collider = v.GetComponent<Collider>();

        if (collider is MeshCollider meshCollider)
        {
            //根据网格碰撞体来创建多边形
        }

        MeshFilter mf = v.GetComponent<MeshFilter>();
        
        if (mf != null && mf.sharedMesh != null )
        {
            //通过网格过滤器创建多边形
        }
        
        throw new InvalidOperationException($"无法从游戏对象 '{v?.name}' 创建凸包");p
        
    }
    
    
    
}