using System.Diagnostics;
using Unity.Mathematics;
using UnityEngine.UI;
using static Unity.Mathematics.math;
using float3 = Unity.Mathematics.float3;

namespace UnityNativeHull
{
    /// <summary>
    /// 无限衍生的平面，可以用一个法线和平面上一个点来确定
    /// </summary>
    [DebuggerDisplay("NativePlane:  {Normal}, {Offset}")]
    public unsafe struct NativePlane
    {
        
        public float3 Normal;
        
        //距离原点的距离，通过平面上一个点p dot Normal可以计算出距离原点的距离
        public float Offset;

        public NativePlane(float3 normal, float offset)
        {
            Normal = normal;
            Offset = offset;
        }
        
        /// <summary>
        /// 一个点到该平面的距离
        /// </summary>
        /// <param name="point"></param>
        /// <returns></returns>
        public float Distance(float3 point)
        {
            return dot(Normal, point) - Offset;
        }
        
        /// <summary>
        /// 和这个点距离最近的平面上的点
        /// </summary>
        /// <param name="point"></param>
        /// <returns></returns>
        public float3 ClosestPoint(float3 point)
        {
            return point - Distance(point) * normalize(Normal);
        }
        
        /// <summary>
        /// 将一个刚体的平面在一个刚体局部坐标系转换到另一个
        /// </summary>
        /// <param name="t"></param>
        /// <param name="plane"></param>
        /// <returns></returns>
        public static NativePlane operator *(RigidTransform t, NativePlane plane)
        {
            float3 normal = mul(t.rot, plane.Normal);
            return new NativePlane(normal, plane.Offset + dot(normal, t.pos)); //?不理解这个转换
        }
        
        
        
    }
}