namespace UnityNativeHull
{
    /// <summary>
    /// 有方向边的表示，一条边虽然属于两个面，但规定使其属于一个面
    /// </summary>
    public struct NativeHalfEdge
    {
        //前一条边的索引
        public int Pre;
        
        //下一条边的索引
        public int Next;
        
        //不在此边所在面中相对的一条边的索引
        public int Twin;
        
        //改边所在面的索引
        public int Face;
        
        //改边所在起点的索引
        public int Origin;
    }
}